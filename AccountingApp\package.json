{"name": "accounting-app", "version": "1.0.0", "description": "نظام المحاسبة المطور - تطبيق Android أصلي", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "clear": "expo start --clear", "build:android": "eas build --platform android --profile production", "build:apk": "eas build --platform android --profile preview"}, "dependencies": {"expo": "~50.0.0", "react": "18.2.0", "react-native": "0.73.6", "@expo/vector-icons": "^14.0.0", "expo-status-bar": "~1.11.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "keywords": ["accounting", "محاسبة", "android", "react-native", "expo"], "author": "Accounting App Developer", "license": "MIT", "private": true}